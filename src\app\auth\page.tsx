"use client";

import type React from "react";
import { useEffect, useState, useRef } from "react";
import { Rubik } from "next/font/google";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { motion, AnimatePresence } from "framer-motion";
import { useRouter } from "next/navigation";
import { supabase } from "../../../supabase/supabaseClient";
import Script from "next/script";
import TosPrivacyModal from "../../components/TosPrivacyModal";

const rubik = Rubik({ subsets: ["latin"] });

const GoogleButton: React.FC<{ onClick: () => void; text: string }> = ({ onClick, text }) => {
  return (
    <button
      onClick={onClick}
      className="flex items-center justify-center w-full bg-white border border-gray-300 text-gray-700 font-medium py-2 px-4 rounded hover:bg-gray-100 transition duration-300"
    >
      <svg className="mr-3" width="18" height="18" viewBox="0 0 18 18">
        <path
          d="M17.64 9.20455c0-.70414-.06364-1.26841-.18182-1.85455H9v3.50818h4.84a4.13182 4.13182 0 01-1.78 2.70636v2.24909h2.875c1.68-1.54818 2.64-3.82636 2.64-6.60909z"
          fill="#4285F4"
        />
        <path
          d="M9 18c2.43 0 4.46591-.80682 5.95455-2.18909l-2.875-2.24909c-.8.54-1.82.855-3.07955.855-2.36 0-4.34818-1.59-5.06545-3.72727H1.01818v2.33455A8.99455 8.99455 0 009 18z"
          fill="#34A853"
        />
        <path
          d="M3.93455 10.6364A5.39636 5.39636 0 013.6 9c0-.61636.21636-1.18545.6-1.63636V4.85364H1.01818A8.99364 8.99364 0 000 9c0 .99.23818 1.93.66818 2.74636L3.93455 10.6364z"
          fill="#FBBC05"
        />
        <path
          d="M9 3.57364c1.32273 0 2.44364.45455 3.35455 1.34818l2.51818-2.51818C13.46273 1.40636 11.43 0.6 9 0.6 5.02 0.6 2.49545 2.37455 1.01818 4.85364l2.91636 2.33455C4.65182 5.16364 6.64545 3.57364 9 3.57364z"
          fill="#EA4335"
        />
      </svg>
      <span className="text-sm">{text}</span>
    </button>
  );
};

export default function LoginSignupPage() {
  const [isLogin, setIsLogin] = useState(true);
  const [showTerms, setShowTerms] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const router = useRouter();

  // Login states
  const [loginEmail, setLoginEmail] = useState("");
  const [loginPassword, setLoginPassword] = useState("");

  // Signup state for regular sign-up
  const [signupData, setSignupData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    age: "",
    education: "",
    password: "",
    confirmPassword: "",
  });

  // Google sign-up extra info state
  const [googleExtra, setGoogleExtra] = useState({
    age: "",
    education: "",
  });
  // This flag will control displaying the extra info form if no user record exists
  const [googleSignup, setGoogleSignup] = useState(false);

  // Current authenticated user state
  const [currentUser, setCurrentUser] = useState<any>(null);

  const [showEmailVerification, setShowEmailVerification] = useState(false);
  const [verificationEmail, setVerificationEmail] = useState("");

  // Newsletter subscription state and ref for external popup trigger
  const [subscribeNewsletter, setSubscribeNewsletter] = useState(false);
  // const newsletterTriggerRef = useRef<HTMLAnchorElement>(null);

  const [tosAccepted, setTosAccepted] = useState(false);
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    document.title = "Klinn | Portal";
  }, []);

  const toggleForm = () => {
    setIsLogin(!isLogin);
    setErrorMessage("");
    setGoogleSignup(false);
  };

  // Typewriter Effect (unchanged)
  const sentences = [
    "Your all-in-one medical career platform.",
    "Search for top clinics near you.",
    "Get expert admissions consulting.",
    "Discover relevant extracurriculars.",
    "Advance your medical career with ease.",
  ];
  const [displayedText, setDisplayedText] = useState("");
  const [currentLineIndex, setCurrentLineIndex] = useState(0);
  const [currentCharIndex, setCurrentCharIndex] = useState(0);
  const typeSpeed = 50;

  useEffect(() => {
    if (currentLineIndex >= sentences.length) return;
    const currentSentence = sentences[currentLineIndex];
    if (currentCharIndex < currentSentence.length) {
      const timeout = setTimeout(() => {
        setDisplayedText((prev) => prev + currentSentence[currentCharIndex]);
        setCurrentCharIndex(currentCharIndex + 1);
      }, typeSpeed);
      return () => clearTimeout(timeout);
    } else {
      const timeout = setTimeout(() => {
        setDisplayedText((prev) => prev + "\n");
        setCurrentLineIndex(currentLineIndex + 1);
        setCurrentCharIndex(0);
      }, 500);
      return () => clearTimeout(timeout);
    }
  }, [currentLineIndex, currentCharIndex]);

  // Check localStorage to see if the user has previously accepted.
  useEffect(() => {
    const accepted = localStorage.getItem("tosAccepted");
    if (accepted) {
      setTosAccepted(true);
    }
  }, []);

  const handleAgree = () => {
    setTosAccepted(true);
    setShowModal(false);
    localStorage.setItem("tosAccepted", "true");
  };

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!tosAccepted) {
      alert("You must accept the Terms of Service and Privacy Policy to sign up.");
      return;
    }
    // Proceed with your signup logic here.
    alert("Form submitted!");
  };

  // Input Handlers
  const handleLoginEmailChange = (e: React.ChangeEvent<HTMLInputElement>) =>
    setLoginEmail(e.target.value);
  const handleLoginPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) =>
    setLoginPassword(e.target.value);
  const handleSignupChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setSignupData((prev) => ({ ...prev, [name]: value }));
  };
  const handleGoogleExtraChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setGoogleExtra((prev) => ({ ...prev, [name]: value }));
  };

 // Newsletter toggle handler
  // const handleNewsletterToggle = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   const checked = e.target.checked;
  //   setSubscribeNewsletter(checked);
  //   if (checked && newsletterTriggerRef.current) {
  //     newsletterTriggerRef.current.click();
  //   }
  // };

  // Login handler using Supabase Auth
  const handleLogin = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setErrorMessage("");
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email: loginEmail,
        password: loginPassword,
      });
      if (error) throw error;
      // On successful login, you could redirect here.
    } catch (error: any) {
      setErrorMessage(error.message);
    }
  };

  // Regular signup handler
  const handleSignup = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setErrorMessage("");

    if (signupData.password !== signupData.confirmPassword) {
      setErrorMessage("Passwords do not match");
      return;
    }

    try {
      const { error } = await supabase.auth.signUp({
        email: signupData.email,
        password: signupData.password,
        options: {
          data: {
            firstName: signupData.firstName,
            lastName: signupData.lastName,
            age: signupData.age,
            education: signupData.education,
          },
        },
      });
      if (error) throw error;

      // Show email verification UI
      setVerificationEmail(signupData.email);
      setShowEmailVerification(true);
    } catch (error: any) {
      setErrorMessage(error.message);
    }
  };

  useEffect(() => {
    if (typeof window !== "undefined") {
      const searchParams = new URLSearchParams(window.location.search);
      const referral = searchParams.get("referral");
      if (referral) {
        // Set a cookie that expires in 7 days
        document.cookie = `referral=${referral}; path=/; max-age=${7 * 24 * 60 * 60}`;
        // Optionally, also set localStorage if you still need it for your non-OAuth flows:
        localStorage.setItem("referral", referral);
      }
    }
  }, []);
  
  

  // --- NEW: Auth Listener with User Record Check ---
  useEffect(() => {
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (session?.user) {
          setCurrentUser(session.user);

          // Check if profile exists and onboarding is complete
          const { data: profile, error } = await supabase
            .from("profiles")
            .select("onboarding_complete")
            .eq("id", session.user.id)
            .single();

          if (error || !profile) {
            console.error("Error fetching profile:", error);
            // If profile doesn't exist, redirect to onboarding
            router.push("/onboard-signup");
            return;
          }

          // Check both possible field names for backward compatibility
          const isOnboardingComplete = profile.onboarding_complete || profile.onboarding_complete;

          if (!isOnboardingComplete) {
            router.push("/onboard-signup");
          } else {
            router.push("/profile-dashboard");
          }
        } else {
          setCurrentUser(null);
        }
      }
    );

    return () => authListener.subscription?.unsubscribe();
  }, [router]);

  // Google sign-up handler for extra info after OAuth redirect.
  const handleGoogleSignUp = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setErrorMessage("");

    if (!googleExtra.age || !googleExtra.education) {
      setErrorMessage("Please provide your age and education level.");
      return;
    }
    if (!currentUser) {
      setErrorMessage("Authentication still loading. Please wait a moment.");
      return;
    }

    try {
      // Update the existing profile with additional info
      const { error } = await supabase.from("profiles").update({
        age: Number.parseInt(googleExtra.age) || null,
        education: googleExtra.education,
        updated_at: new Date().toISOString(),
      }).eq("id", currentUser.id);

      if (error) throw error;

      // Redirect to onboarding (though they might go straight to dashboard if already completed)
      router.push("/onboard-signup");
    } catch (err: any) {
      setErrorMessage(err.message);
    }
  };

  const handleGoogleLogin = async () => {
    const { error } = await supabase.auth.signInWithOAuth({
      provider: "google",
      options: {
        redirectTo: `${window.location.origin}/api/auth/google/callback`,
        scopes: "openid email profile https://www.googleapis.com/auth/gmail.send",
        queryParams: {
          access_type: "offline",
          prompt: "consent",
        },
      },
    });
    if (error) {
      console.error("Google OAuth sign-in error:", error);
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-[#12100E] to-[#93C5FD] font-rubik">
      <Navbar />

      <main className="flex-grow flex items-center justify-center p-6 pt-12 min-h-screen mt-6">
        <div className="container mx-auto flex flex-col lg:flex-row items-center justify-center lg:space-y-0 lg:space-x-40">
          {/* Hero Section */}
          <motion.div
            className="w-full lg:w-1/2 text-center lg:text-left"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.7 }}
          >
            <h1 className="text-5xl lg:text-6xl font-bold mb-6 lg:mt-[-85px] text-white">
              Welcome to Klinn<span className="animate-blink">_</span>
            </h1>
            <pre className={`px-2 text-xl mb-24 text-white/90 sm:h-[100px] whitespace-pre-wrap ${rubik.className}`}>
              {displayedText}
              {currentLineIndex < sentences.length && <span className="animate-blink">_</span>}
            </pre>
          </motion.div>

          {/* Divider */}
          <div className="hidden lg:block h-[625px] w-full lg:w-[1px] bg-white mr-8"></div>

          {/* Form Section */}
          <AnimatePresence mode="wait">
            <motion.div
              key={isLogin ? "login" : googleSignup ? "google-signup" : "signup"}
              className="w-full lg:w-1/2"
              initial={{ opacity: 0, translateY: -170 }}
              animate={{ opacity: 1, translateY: -106 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.75 }}
            >
              {/* Toggle Switch */}
              <div className="flex justify-center mb-6 mt-24">
                <div className="bg-white/20 rounded-full p-1 flex">
                  <button
                    onClick={() => {
                      setIsLogin(true);
                      setGoogleSignup(false);
                    }}
                    className={`px-6 py-2 rounded-full transition-colors duration-300 ${
                      isLogin ? "bg-white text-[#1E3A8A]" : "text-white"
                    }`}
                  >
                    Login
                  </button>
                  <button
                    onClick={() => {
                      setIsLogin(false);
                      setGoogleSignup(false);
                    }}
                    className={`px-6 py-2 rounded-full transition-colors duration-300 ${
                      !isLogin ? "bg-white text-[#1E3A8A]" : "text-white"
                    }`}
                  >
                    Sign Up
                  </button>
                </div>
              </div>

              {/* Form */}
              {isLogin ? (
                <>
                  <form onSubmit={handleLogin} className="space-y-5">
                    <div className="space-y-2">
                      <label htmlFor="email" className="text-sm font-medium text-white/90">
                        Email Address
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        placeholder="<EMAIL>"
                        required
                        value={loginEmail}
                        onChange={handleLoginEmailChange}
                        className="w-full px-4 py-3 rounded-lg bg-transparent border border-white/50 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 transition duration-300"
                      />
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="password" className="text-sm font-medium text-white/90">
                        Password
                      </label>
                      <input
                        type="password"
                        id="password"
                        name="password"
                        placeholder="••••••••"
                        required
                        value={loginPassword}
                        onChange={handleLoginPasswordChange}
                        className="w-full px-4 py-3 rounded-lg bg-transparent border border-white/50 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 transition duration-300"
                      />
                    </div>
                    {errorMessage && <p className="text-red-500 text-sm">{errorMessage}</p>}
                    <div className="flex justify-between items-center">
                      {/* Redirect to Forgot Password page */}
                      <button
                        type="button"
                        onClick={() => router.push("/auth/forgot-password")}
                        className="text-sm text-white hover:underline"
                      >
                        Forgot password?
                      </button>
                    </div>
                    <button
                      type="submit"
                      className="w-full bg-white text-[#1E3A8A] font-bold py-3 rounded-lg hover:bg-opacity-90 transition duration-300"
                    >
                      Log In
                    </button>
                  </form>
                  <div className="flex justify-center mt-4">
                    <GoogleButton onClick={handleGoogleLogin} text="Log In with Google" />
                  </div>
                  <p className="text-center text-white mt-4">
                    Don&apos;t have an account?{" "}
                    <button type="button" onClick={toggleForm} className="text-white font-semibold hover:underline">
                      Sign up
                    </button>
                  </p>
                </>
              ) : (
                <>
                  {googleSignup ? (
                    <form onSubmit={handleGoogleSignUp} className="space-y-5">
                      <div className="space-y-2">
                        <label htmlFor="age" className="text-sm font-medium text-white/90">
                          Age
                        </label>
                        <input
                          type="text"
                          id="age"
                          name="age"
                          placeholder="25"
                          required
                          value={googleExtra.age}
                          onChange={handleGoogleExtraChange}
                          className="w-full px-4 py-3 rounded-lg bg-transparent border border-white/50 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 transition duration-300"
                        />
                      </div>
                      <div className="space-y-2">
                        <label htmlFor="education" className="text-sm font-medium text-white/90">
                          Education Level
                        </label>
                        <select
                          id="education"
                          name="education"
                          required
                          value={googleExtra.education}
                          onChange={handleGoogleExtraChange}
                          className="w-full px-4 py-3 rounded-lg bg-transparent border border-white/50 text-white focus:outline-none focus:ring-2 focus:ring-white/50 transition duration-300 [&>option]:text-black"
                        >
                          <option value="" disabled>
                            Select level
                          </option>
                          <option value="student">Student</option>
                          <option value="other">Other</option>
                        </select>
                      </div>
                      {errorMessage && <p className="text-red-500 text-sm">{errorMessage}</p>}
                      <button
                        type="submit"
                        className="w-full bg-white text-[#1E3A8A] font-bold py-3 rounded-lg hover:bg-opacity-90 transition duration-300"
                      >
                        Continue with Google
                      </button>
                      <p className="text-center text-white mt-4">
                        Or{" "}
                        <button
                          type="button"
                          onClick={() => setGoogleSignup(false)}
                          className="text-white font-semibold hover:underline"
                        >
                          go back
                        </button>
                      </p>
                    </form>
                  ) : (
                    <form onSubmit={handleSignup} className="space-y-5">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <label htmlFor="firstName" className="text-sm font-medium text-white/90">
                            First Name
                          </label>
                          <input
                            type="text"
                            id="firstName"
                            name="firstName"
                            placeholder="John"
                            required
                            value={signupData.firstName}
                            onChange={handleSignupChange}
                            className="w-full px-4 py-3 rounded-lg bg-transparent border border-white/50 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 transition duration-300"
                          />
                        </div>
                        <div className="space-y-2">
                          <label htmlFor="lastName" className="text-sm font-medium text-white/90">
                            Last Name
                          </label>
                          <input
                            type="text"
                            id="lastName"
                            name="lastName"
                            placeholder="Doe"
                            required
                            value={signupData.lastName}
                            onChange={handleSignupChange}
                            className="w-full px-4 py-3 rounded-lg bg-transparent border border-white/50 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 transition duration-300"
                          />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <label htmlFor="email" className="text-sm font-medium text-white/90">
                          Email Address
                        </label>
                        <input
                          type="email"
                          id="email"
                          name="email"
                          placeholder="<EMAIL>"
                          required
                          value={signupData.email}
                          onChange={handleSignupChange}
                          className="w-full px-4 py-3 rounded-lg bg-transparent border border-white/50 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 transition duration-300"
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <label htmlFor="age" className="text-sm font-medium text-white/90">
                            Age
                          </label>
                          <input
                            type="text"
                            id="age"
                            name="age"
                            placeholder="25"
                            required
                            value={signupData.age}
                            onChange={handleSignupChange}
                            className="w-full px-4 py-3 rounded-lg bg-transparent border border-white/50 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 transition duration-300"
                          />
                        </div>
                        <div className="space-y-2">
                          <label htmlFor="education" className="text-sm font-medium text-white/90">
                            Education Level
                          </label>
                          <select
                            id="education"
                            name="education"
                            required
                            value={signupData.education}
                            onChange={handleSignupChange}
                            className="w-full px-4 py-3 rounded-lg bg-transparent border border-white/50 text-white focus:outline-none focus:ring-2 focus:ring-white/50 transition duration-300 [&>option]:text-black"
                          >
                            <option value="" disabled>
                              Select level
                            </option>
                            <option value="other">Other</option>
                            <option value="elementary">Elementary</option>
                            <option value="middle_school">Middle School</option>
                            <option value="high_school">High School</option>
                            <option value="bachelors">Bachelor&apos;s Degree</option>
                            <option value="masters">Master&apos;s Degree</option>
                            <option value="phd">Ph.D.</option>
                          </select>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <label htmlFor="password" className="text-sm font-medium text-white/90">
                          Password
                        </label>
                        <input
                          type="password"
                          id="password"
                          name="password"
                          placeholder="••••••••"
                          required
                          value={signupData.password}
                          onChange={handleSignupChange}
                          className="w-full px-4 py-3 rounded-lg bg-transparent border border-white/50 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 transition duration-300"
                        />
                      </div>
                      <div className="space-y-2">
                        <label htmlFor="confirmPassword" className="text-sm font-medium text-white/90">
                          Confirm Password
                        </label>
                        <input
                          type="password"
                          id="confirmPassword"
                          name="confirmPassword"
                          placeholder="••••••••"
                          required
                          value={signupData.confirmPassword}
                          onChange={handleSignupChange}
                          className="w-full px-4 py-3 rounded-lg bg-transparent border border-white/50 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 transition duration-300"
                        />
                      </div>
                      {/* Terms & Conditions Block */}
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="terms"
                          name="terms"
                          checked={tosAccepted}
                          onChange={(e) => {
                            if (!e.target.checked) {
                              setTosAccepted(false);
                            } else if (!tosAccepted) {
                              setShowModal(true);
                            }
                          }}
                          className="form-checkbox h-5 w-5 text-white border-white/50 rounded focus:ring-2 focus:ring-white/50"
                        />
                        <label htmlFor="terms" className="text-sm text-white">
                          I agree to the{" "}
                          <button
                            type="button"
                            onClick={() => setShowModal(true)}
                            className="text-white underline"
                          >
                            Terms of Service & Privacy Policy
                          </button>
                        </label>
                      </div>
                      {/* Newsletter Checkbox
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="newsletter"
                          name="newsletter"
                          checked={subscribeNewsletter}
                          onChange={handleNewsletterToggle}
                          className="form-checkbox h-5 w-5 text-white border-white/50 rounded focus:ring-2 focus:ring-white/50"
                        />
                        <label htmlFor="newsletter" className="text-sm text-white">
                          Subscribe to our Newsletter?
                        </label>
                      </div> */}
                      {/* Hidden trigger for external newsletter popup */}
                      {/* <a
                        ref={newsletterTriggerRef}
                        data-formkit-toggle="397be323b5"
                        href="https://klinn.kit.com/397be323b5"
                        style={{ display: "none" }}
                      >
                        Newsletter Toggle
                      </a> */}
                      {errorMessage && <p className="text-red-500 text-sm">{errorMessage}</p>}
                      <button
                        type="submit"
                        className="w-full bg-white text-[#1E3A8A] font-bold py-3 rounded-lg hover:bg-opacity-90 transition duration-300"
                      >
                        Sign Up
                      </button>
                      <div className="flex justify-center mt-4">
                        <GoogleButton onClick={handleGoogleLogin} text="Sign Up with Google" />
                      </div>
                      <p className="text-center text-white mt-4">
                        Already have an account?{" "}
                        <button type="button" onClick={toggleForm} className="text-white font-semibold hover:underline">
                          Log in
                        </button>
                      </p>
                    </form>
                  )}
                </>
              )}
            </motion.div>
          </AnimatePresence>
        </div>
      </main>

      {/* Email Verification Modal */}
      {showEmailVerification && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <motion.div
            className="bg-white rounded-lg p-8 max-w-md w-full shadow-xl"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 mb-6">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-8 w-8 text-blue-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                  />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-gray-800 mb-2">Verify your email</h2>
              <p className="text-gray-600 mb-6">
                We&apos;ve sent a verification link to{" "}
                <span className="font-medium text-blue-600">{verificationEmail}</span>. Please check your inbox and click the link to complete your registration.
              </p>
              <div className="space-y-3">
                <button
                  onClick={() => setShowEmailVerification(false)}
                  className="w-full bg-blue-600 text-white font-bold py-3 rounded-lg hover:bg-blue-700 transition duration-300"
                >
                  Got it
                </button>
                <button
                  onClick={() => {
                    setShowEmailVerification(false);
                    setIsLogin(true);
                  }}
                  className="w-full bg-transparent text-blue-600 font-medium py-3 rounded-lg hover:bg-blue-50 transition duration-300"
                >
                  Back to login
                </button>
              </div>
              <p className="mt-4 text-sm text-gray-500">
                Didn&apos;t receive the email? Check your spam folder or{" "}
                <button
                  className="text-blue-600 hover:underline"
                  onClick={async () => {
                    try {
                      const { error } = await supabase.auth.resend({
                        type: "signup",
                        email: verificationEmail,
                      });
                      if (error) throw error;
                      setErrorMessage("Verification email resent!");
                      setTimeout(() => setErrorMessage(""), 3000);
                    } catch (error: any) {
                      setErrorMessage(error.message);
                    }
                  }}
                >
                  click here to resend
                </button>
              </p>
              {errorMessage && <p className="mt-2 text-sm text-red-500">{errorMessage}</p>}
            </div>
          </motion.div>
        </div>
      )}

      {showTerms && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg p-6 max-w-md w-full overflow-y-auto h-[80vh]">
            <h2 className="text-2xl font-bold mb-4">Terms and Conditions</h2>
            <p className="mb-4">
              Welcome to Klinn, a platform designed to connect users with medical consultants, clinics, job opportunities, and more.
              By accessing or using our services, you agree to comply with and be bound by these terms and conditions.
              Please read them carefully. If you do not agree, you may not access our website or services.
            </p>
            <button onClick={() => setShowTerms(false)} className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
              Close
            </button>
          </div>
        </div>
      )}

      <Footer className="bg-transparent text-white p-8 transition-all duration-300 ease-in-out mt-auto" />
      <Script async data-uid="397be323b5" src="https://klinn.kit.com/397be323b5/index.js" strategy="afterInteractive" />
      {showModal && (
        <TosPrivacyModal isOpen={showModal} onAgree={handleAgree} onClose={() => setShowModal(false)} />
      )}
    </div>
  );
}
