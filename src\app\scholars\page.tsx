'use client';

import React, { useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Footer from '@/components/Footer';
import { CheckCircle, Gift, Award, Sparkles, ArrowRight, Users, Rocket } from 'lucide-react';
import { motion } from 'framer-motion';
import Navbar from '@/components/Navbar';

const fadeInUpVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 }
};

const staggerChildren = {
  visible: {
    transition: {
      staggerChildren: 0.2
    }
  }
};

type PartnerBenefit = {
  name: string;
  description: string;
};

type PartnerCompany = {
  name: string;
  logo: string;
  description: string;
  benefits: PartnerBenefit[];
  ctaText: string;
  ctaLink: string;
  bgColor: string;
};

const partnerCompanies: PartnerCompany[] = [
  {
    name: "Sprint.dev",
    logo: "/images/sprintdev-logo.png",
    description: "Sprint.dev (formerly nosu.io) provides cutting-edge developer tools and resources for students.",
    benefits: [
      { name: "Premium Subscription", description: "6-month free access to all premium development tools" },
      { name: "Mentorship", description: "Monthly 1:1 sessions with experienced developers" },
      { name: "Exclusive Workshops", description: "Access to coding workshops and hackathons" },
      { name: "Project Feedback", description: "Code reviews from industry professionals" }
    ],
    ctaText: "Claim Sprint.dev Benefits",
    ctaLink: "/claim/sprintdev",
    bgColor: "from-blue-400 to-blue-500"
  },
  {
    name: "Crackd.it",
    logo: "/images/crackdit-logo.png",
    description: "Crackd.it offers specialized test prep and academic resources for healthcare students.",
    benefits: [
      { name: "Test Prep Materials", description: "Complete MCAT/DAT/OAT prep material access" },
      { name: "Study Groups", description: "Join exclusive study cohorts with other pre-health students" },
      { name: "Question Banks", description: "Unlimited access to medical exam question banks" },
      { name: "Performance Analytics", description: "Detailed study progress tracking and analytics" }
    ],
    ctaText: "Access Crackd.it Resources",
    ctaLink: "/claim/crackdit",
    bgColor: "from-blue-300 to-blue-400"
  },
  {
    name: "Runway Mobile App",
    logo: "/images/runway-logo.png",
    description: "Runway Mobile App helps students manage their academic and professional journeys.",
    benefits: [
      { name: "Pro Subscription", description: "1-year free Runway Pro subscription ($120 value)" },
      { name: "Career Planning Tools", description: "Access to premium career roadmap features" },
      { name: "Application Tracker", description: "Track your medical school applications effortlessly" },
      { name: "Goal Setting Features", description: "Advanced goal tracking and achievement tools" }
    ],
    ctaText: "Get Runway Pro Access",
    ctaLink: "/claim/runway",
    bgColor: "from-blue-500 to-blue-600"
  },
  {
    name: "Schoolnest",
    logo: "/images/schoolnest-logo.png",
    description: "Schoolnest provides housing and community solutions for healthcare students.",
    benefits: [
      { name: "Housing Discounts", description: "10% off first month's rent at partner locations" },
      { name: "Roommate Matching", description: "Premium roommate matching service for med students" },
      { name: "Community Events", description: "Priority access to networking events and socials" },
      { name: "Moving Assistance", description: "Special rates on moving and storage services" }
    ],
    ctaText: "Explore Schoolnest Benefits",
    ctaLink: "/claim/schoolnest",
    bgColor: "from-blue-400 to-blue-500"
  }
];

const StudentScholarsPage = () => {
  useEffect(() => {
    document.title = 'Klinn | Student Scholars Program';
  }, []); 

  return (
    <div className="flex flex-col min-h-screen bg-white font-light">
      <Navbar />
      
      {/* Hero Section */}
      <section className="pt-32 pb-28 bg-gradient-to-b from-blue-500 to-blue-400">
        <div className="container mx-auto px-4">
          <motion.div 
            className="max-w-2xl mx-auto text-center text-white"
            initial="hidden"
            animate="visible"
            variants={staggerChildren}
          >
            <motion.div 
              className="mb-6 inline-block"
              variants={fadeInUpVariants}
            >
              <Badge className="px-3 py-1 bg-white/20 backdrop-blur-sm text-white font-light text-xs">
                <Sparkles className="w-3 h-3 mr-1" /> Exclusive Program
              </Badge>
            </motion.div>
            <motion.h1 
              className="text-5xl font-extralight mb-6 tracking-tight leading-tight"
              variants={fadeInUpVariants}
            >
              Klinn Student Scholars
            </motion.h1>
            <motion.p 
              className="text-base font-extralight mb-10 opacity-90 leading-relaxed"
              variants={fadeInUpVariants}
            >
              Access exclusive benefits, resources, and opportunities from our partner companies to accelerate your healthcare career.
            </motion.p>
            <motion.div 
              variants={fadeInUpVariants}
            >
              <Button 
                size="lg" 
                className="bg-white text-blue-600 hover:bg-white/90 font-light rounded-full px-8"
              >
                Apply Now
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </section>
      
      {/* Main Content */}
      <main className="flex-grow">
        {/* Program Overview */}
        <section className="py-28 bg-white">
          <div className="container mx-auto px-4">
            <motion.div 
              className="max-w-2xl mx-auto text-center mb-20"
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={staggerChildren}
            >
              <motion.h2 
                className="text-2xl font-extralight mb-5 text-gray-800"
                variants={fadeInUpVariants}
              >
                Why Join Klinn Student Scholars?
              </motion.h2>
              <motion.p 
                className="text-gray-500 font-light leading-relaxed"
                variants={fadeInUpVariants}
              >
                Our Student Scholars program connects ambitious pre-med and healthcare students with industry-leading resources and tools. Membership is free and provides exclusive benefits worth over $500.
              </motion.p>
            </motion.div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-10 max-w-4xl mx-auto">
              <motion.div
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                variants={fadeInUpVariants}
                transition={{ delay: 0.1 }}
              >
                <Card className="h-full border-0 shadow-none hover:shadow-sm transition-shadow duration-300">
                  <CardHeader className="text-center pb-2">
                    <Gift className="w-5 h-5 text-blue-500 mx-auto mb-4 opacity-80" />
                    <CardTitle className="text-base text-gray-700 font-light">Premium Resources</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-500 text-center text-sm font-light leading-relaxed">
                      Access tools and resources typically behind paywalls, completely free for Klinn Scholars.
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
              
              <motion.div
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                variants={fadeInUpVariants}
                transition={{ delay: 0.2 }}
              >
                <Card className="h-full border-0 shadow-none hover:shadow-sm transition-shadow duration-300">
                  <CardHeader className="text-center pb-2">
                    <Users className="w-5 h-5 text-blue-500 mx-auto mb-4 opacity-80" />
                    <CardTitle className="text-base text-gray-700 font-light">Exclusive Community</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-500 text-center text-sm font-light leading-relaxed">
                      Join a network of like-minded students and professionals in the healthcare field.
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
              
              <motion.div
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                variants={fadeInUpVariants}
                transition={{ delay: 0.3 }}
              >
                <Card className="h-full border-0 shadow-none hover:shadow-sm transition-shadow duration-300">
                  <CardHeader className="text-center pb-2">
                    <Rocket className="w-5 h-5 text-blue-500 mx-auto mb-4 opacity-80" />
                    <CardTitle className="text-base text-gray-700 font-light">Career Acceleration</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-500 text-center text-sm font-light leading-relaxed">
                      Get a head start on your healthcare career with specialized tools and mentorship.
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </div>
        </section>
        
        {/* Partner Companies */}
        <section className="py-28 bg-gray-50">
          <div className="container mx-auto px-4">
            <motion.h2 
              className="text-2xl font-extralight mb-16 text-center text-gray-800"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
            >
              Exclusive Partner Benefits
            </motion.h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
              {partnerCompanies.map((company, index) => (
                <motion.div
                  key={index}
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true }}
                  variants={fadeInUpVariants}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="overflow-hidden border-0 shadow-sm hover:shadow-md transition-all duration-300">
                    <div className={`bg-gradient-to-r ${company.bgColor} p-3 flex items-center`}>
                      <div className="w-7 h-7 bg-white/90 rounded-full flex items-center justify-center mr-3">
                        <div className="font-light text-blue-600 text-xs">
                          {company.name.split(' ')[0].charAt(0)}
                        </div>
                      </div>
                      <h3 className="text-base font-light text-white">{company.name}</h3>
                    </div>
                    
                    <CardContent className="p-6">
                      <p className="text-gray-500 text-xs font-light mb-5 leading-relaxed">{company.description}</p>
                      
                      <h4 className="font-light text-gray-700 mb-4 text-xs flex items-center">
                        <Award className="mr-1 h-3 w-3 opacity-70" />
                        Scholar Benefits
                      </h4>
                      
                      <ul className="space-y-3 mb-6">
                        {company.benefits.map((benefit, i) => (
                          <li key={i} className="flex text-xs">
                            <CheckCircle className="h-3 w-3 text-green-400 flex-shrink-0 mr-2 mt-1" />
                            <div>
                              <p className="font-normal text-gray-700">{benefit.name}</p>
                              <p className="text-xs text-gray-500 font-light">{benefit.description}</p>
                            </div>
                          </li>
                        ))}
                      </ul>
                    
                      <Button 
                        className="w-full bg-transparent text-blue-500 hover:bg-blue-50 border border-blue-200 mt-2 text-xs h-9 font-light rounded-full"
                      >
                        {company.ctaText}
                        <ArrowRight className="ml-1 h-3 w-3" />
                      </Button>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </section>
        
        {/* How To Apply */}
        <section className="py-28 bg-white">
          <motion.div 
            className="container mx-auto px-4"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerChildren}
          >
            <motion.h2 
              className="text-2xl font-extralight mb-16 text-center text-gray-800"
              variants={fadeInUpVariants}
            >
              How to Become a Klinn Scholar
            </motion.h2>
            
            <div className="max-w-3xl mx-auto">
              <div className="flex flex-col md:flex-row gap-12 mb-16">
                <motion.div 
                  variants={fadeInUpVariants}
                  transition={{ delay: 0.1 }}
                  className="flex-1"
                >
                  <div className="flex flex-col items-center">
                    <div className="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mb-4">
                      <span className="text-sm font-light text-blue-500">1</span>
                    </div>
                    <h3 className="text-base font-light text-gray-700 mb-3">Apply Online</h3>
                    <p className="text-gray-500 text-center text-sm font-light leading-relaxed">
                      Fill out a simple application form and tell us about your healthcare journey.
                    </p>
                  </div>
                </motion.div>
                
                <motion.div 
                  variants={fadeInUpVariants}
                  transition={{ delay: 0.2 }}
                  className="flex-1"
                >
                  <div className="flex flex-col items-center">
                    <div className="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mb-4">
                      <span className="text-sm font-light text-blue-500">2</span>
                    </div>
                    <h3 className="text-base font-light text-gray-700 mb-3">Get Approved</h3>
                    <p className="text-gray-500 text-center text-sm font-light leading-relaxed">
                      Applications are reviewed within 48 hours. Acceptance rate is over 90%.
                    </p>
                  </div>
                </motion.div>
                
                <motion.div 
                  variants={fadeInUpVariants}
                  transition={{ delay: 0.3 }}
                  className="flex-1"
                >
                  <div className="flex flex-col items-center">
                    <div className="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mb-4">
                      <span className="text-sm font-light text-blue-500">3</span>
                    </div>
                    <h3 className="text-base font-light text-gray-700 mb-3">Access Benefits</h3>
                    <p className="text-gray-500 text-center text-sm font-light leading-relaxed">
                      Receive your Scholar ID and unlock all partner benefits instantly.
                    </p>
                  </div>
                </motion.div>
              </div>
              
              <motion.div 
                className="text-center"
                variants={fadeInUpVariants}
              >
                <Button 
                  size="lg" 
                  className="bg-blue-500 text-white hover:bg-blue-600 font-light rounded-full px-8"
                >
                  Apply to Become a Scholar
                  <ArrowRight className="ml-1 h-4 w-4" />
                </Button>
                <p className="mt-4 text-gray-500 text-xs font-light">
                  Applications for the Spring 2025 cohort close on April 15th
                </p>
              </motion.div>
            </div>
          </motion.div>
        </section>
        
        {/* FAQ Section */}
        <section className="py-28 bg-gray-50">
          <div className="container mx-auto px-4">
            <motion.h2 
              className="text-2xl font-extralight mb-16 text-center text-gray-800"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
            >
              Frequently Asked Questions
            </motion.h2>
            
            <div className="max-w-2xl mx-auto">
              <div className="space-y-5">
                {[
                  {
                    question: "Is the Klinn Student Scholars program really free?",
                    answer: "Yes! There is absolutely no cost to join or maintain your Klinn Scholar status. We've partnered with companies who want to support future healthcare professionals."
                  },
                  {
                    question: "Who is eligible to become a Klinn Scholar?",
                    answer: "Any student pursuing or interested in a healthcare career is eligible. This includes high school students, undergraduates, post-baccalaureate students, and graduate students."
                  },
                  {
                    question: "How long do the benefits last?",
                    answer: "Most benefits are valid for 6-12 months from activation. You can renew your Scholar status annually as long as you remain a student."
                  },
                  {
                    question: "Can I share my benefits with friends?",
                    answer: "Scholar benefits are non-transferable and linked to your individual account. However, you can refer friends to apply for their own Scholar membership!"
                  }
                ].map((faq, index) => (
                  <motion.div
                    key={index}
                    initial="hidden"
                    whileInView="visible"
                    viewport={{ once: true }}
                    variants={fadeInUpVariants}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Card className="border-0 shadow-none bg-white hover:shadow-sm transition-shadow duration-300">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm text-gray-700 font-normal">{faq.question}</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-gray-500 text-xs font-light leading-relaxed">{faq.answer}</p>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
              
              <motion.div 
                className="mt-12 text-center"
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                variants={fadeInUpVariants}
              >
                <p className="text-gray-600 mb-4 text-xs font-light">
                  Have more questions? We&apos;re here to help!
                </p>
                <Link href="/contact" passHref>
                  <Button 
                    variant="outline" 
                    className="text-blue-500 border border-blue-200 hover:bg-blue-50 text-xs font-light rounded-full px-6"
                    size="sm"
                  >
                    Contact Support
                  </Button>
                </Link>
              </motion.div>
            </div>
          </div>
        </section>
        
        {/* Call to Action */}
        <section className="py-24 bg-gradient-to-r from-blue-400 to-blue-300 text-white">
          <motion.div 
            className="container mx-auto px-4 text-center"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerChildren}
          >
            <motion.h2 
              className="text-3xl font-extralight mb-5"
              variants={fadeInUpVariants}
            >
              Join Over 5,000 Klinn Scholars Today
            </motion.h2>
            <motion.p 
              className="text-base font-extralight mb-10 max-w-lg mx-auto opacity-90 leading-relaxed"
              variants={fadeInUpVariants}
            >
              Take the next step in your healthcare journey with exclusive resources, tools, and opportunities.
            </motion.p>
            <motion.div 
              variants={fadeInUpVariants}
            >
              <Button 
                size="lg" 
                className="bg-white text-blue-500 hover:bg-white/90 font-light rounded-full px-8"
              >
                Apply Now
                <ArrowRight className="ml-1 h-4 w-4" />
              </Button>
            </motion.div>
          </motion.div>
        </section>
      </main>

      <Footer className="bg-blue-500 text-white py-8 px-4" />
    </div>
  );
};

export default StudentScholarsPage;