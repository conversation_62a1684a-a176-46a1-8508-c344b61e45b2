import React, { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';
import { GraduationCap, BookOpen, Stethoscope, Award, Users, Heart, Clock, Shield } from 'lucide-react';

interface CardData {
  title: string;
  description: string;
  icon: React.ReactNode;
}

const ScrollingCards: React.FC = () => {
  const topRowRef = useRef<HTMLDivElement>(null);
  const bottomRowRef = useRef<HTMLDivElement>(null);
  const [hoveredCardId, setHoveredCardId] = useState<string | null>(null);
  const [topScrollPosition, setTopScrollPosition] = useState(0);
  const [bottomScrollPosition, setBottomScrollPosition] = useState(0);
  const [topMaxScroll, setTopMaxScroll] = useState(0);
  const [bottomMaxScroll, setBottomMaxScroll] = useState(0);
  const topAnimationRef = useRef<number>();
  const bottomAnimationRef = useRef<number>();

  const topRowCards: CardData[] = [
    { 
      title: "For Students, By Students", 
      description: "We understand the struggle. That’s why we’re fighting the status-quo by building tools to help students find opportunities, apply to them, and create them.",
      icon: <GraduationCap className="w-5 h-5 text-blue-600" strokeWidth={1.5} />
    },
    { 
      title: "Comprehensive Support", 
      description: "From clinical shadowing opportunities to personal statement reviews and interview preparation.",
      icon: <Shield className="w-5 h-5 text-blue-600" strokeWidth={1.5} />
    },
    { 
      title: "In-Depth Database", 
      description: "Explore from 11,000+ clinics, 1500+ professors, 1000+ startups, and 200+ extracurricular opportunities.",
      icon: <Users className="w-5 h-5 text-blue-600" strokeWidth={1.5} />
    },
    { 
      title: "Diverse Expertise", 
      description: "Access advisors from various disciplines including pre-med, BS/MD, nursing, pharmacy, and more.",
      icon: <Award className="w-5 h-5 text-blue-600" strokeWidth={1.5} />
    }
  ];

  const bottomRowCards: CardData[] = [
    { 
      title: "Clinical Experience", 
      description: "Secure the crucial clinical hours required for competitive medical school applications.",
      icon: <Stethoscope className="w-5 h-5 text-blue-600" strokeWidth={1.5} />
    },
    { 
      title: "Centralized Opportunities", 
      description: "The only platform for medicine and research where you can apply to existing opportunities and create your own opportunities via cold outreach.",
      icon: <BookOpen className="w-5 h-5 text-blue-600" strokeWidth={1.5} />
    },
    { 
      title: "Holistic Development", 
      description: "Focus on your overall growth as a healthcare professional, not just application metrics.",
      icon: <Heart className="w-5 h-5 text-blue-600" strokeWidth={1.5} />
    },
    { 
      title: "Personalized Suggestions", 
      description: "Our AI tools suggest opportunities which may be a fit for you. We also provide grammarly-like suggestions to personalize your emails.",
      icon: <Clock className="w-5 h-5 text-blue-600" strokeWidth={1.5} />
    }
  ];

  useEffect(() => {
    const updateScrollWidths = () => {
      if (topRowRef.current) {
        setTopMaxScroll(topRowRef.current.scrollWidth - topRowRef.current.clientWidth);
      }
      if (bottomRowRef.current) {
        setBottomMaxScroll(bottomRowRef.current.scrollWidth - bottomRowRef.current.clientWidth);
      }
    };

    updateScrollWidths();
    window.addEventListener('resize', updateScrollWidths);
    
    return () => {
      window.removeEventListener('resize', updateScrollWidths);
    };
  }, []);

  useEffect(() => {
    const scrollSpeed = 0.5;
    
    const scrollTop = () => {
      if (topRowRef.current && !hoveredCardId) {
        let newPosition = topScrollPosition - scrollSpeed;
        
        if (newPosition <= 0) {
          const resetPosition = topMaxScroll / 3;
          newPosition = resetPosition;
        }
        
        setTopScrollPosition(newPosition);
        topRowRef.current.scrollLeft = newPosition;
      }
      topAnimationRef.current = requestAnimationFrame(scrollTop);
    };

    topAnimationRef.current = requestAnimationFrame(scrollTop);
    
    return () => {
      if (topAnimationRef.current) {
        cancelAnimationFrame(topAnimationRef.current);
      }
    };
  }, [hoveredCardId, topScrollPosition, topMaxScroll]);

  useEffect(() => {
    const scrollSpeed = 0.5;
    
    const scrollBottom = () => {
      if (bottomRowRef.current && !hoveredCardId) {
        let newPosition = bottomScrollPosition + scrollSpeed;
        
        if (newPosition >= bottomMaxScroll) {
          const resetPosition = bottomMaxScroll / 3;
          newPosition = resetPosition;
        }
        
        setBottomScrollPosition(newPosition);
        bottomRowRef.current.scrollLeft = newPosition;
      }
      bottomAnimationRef.current = requestAnimationFrame(scrollBottom);
    };

    bottomAnimationRef.current = requestAnimationFrame(scrollBottom);
    
    return () => {
      if (bottomAnimationRef.current) {
        cancelAnimationFrame(bottomAnimationRef.current);
      }
    };
  }, [hoveredCardId, bottomScrollPosition, bottomMaxScroll]);

  const fadeInUpVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }
  };

  return (
    <motion.section 
      className="py-16 bg-blue-200 text-blue-900 overflow-hidden"
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true }}
      variants={{
        hidden: {},
        visible: { transition: { staggerChildren: 0.1 } }
      }}
    >
      <div className="container mx-auto px-4 max-w-5xl">
        <motion.h2 
          className="text-2xl font-light mb-10 text-center"
          variants={fadeInUpVariants}
        >
          Why Choose Klinn
        </motion.h2>
        
        <motion.div 
          className="mb-8"
          variants={fadeInUpVariants}
        >
          <div className="relative">
            <div className="absolute left-0 top-0 bottom-0 w-8 bg-gradient-to-r from-blue-200 to-transparent z-10"></div>
            <div className="absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-blue-200 to-transparent z-10"></div>
            
            <div 
              ref={topRowRef}
              className="flex overflow-x-auto scrollbar-hide pb-3"
              style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
            >
              <div className="flex gap-6 px-12">
                {[...topRowCards, ...topRowCards, ...topRowCards].map((card, index) => (
                  <motion.div 
                    key={`top-card-${index}`}
                    className="min-w-72 max-w-72 bg-white border border-gray-100 rounded-lg p-6 shadow-sm hover:shadow-md flex flex-col"
                    onMouseEnter={() => setHoveredCardId(`top-card-${index}`)}
                    onMouseLeave={() => setHoveredCardId(null)}
                    initial={{ opacity: 0 }}
                    animate={{ 
                      opacity: 1,
                      scale: hoveredCardId === `top-card-${index}` ? 1.02 : 1,
                      transition: { duration: 0.3 }
                    }}
                    whileHover={{ boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)" }}
                    style={{ zIndex: hoveredCardId === `top-card-${index}` ? 10 : 1 }}
                  >
                    <div className="flex items-center mb-3">
                      <div className="mr-3">{card.icon}</div>
                      <h3 className="text-base font-medium text-blue-800">{card.title}</h3>
                    </div>
                    <p className="text-blue-800/80 text-sm">{card.description}</p>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </motion.div>
        
        <motion.div 
          variants={fadeInUpVariants}
          transition={{ delay: 0.2 }}
        >
          <div className="relative">
            <div className="absolute left-0 top-0 bottom-0 w-8 bg-gradient-to-r from-blue-200 to-transparent z-10"></div>
            <div className="absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-blue-200 to-transparent z-10"></div>
            
            <div 
              ref={bottomRowRef}
              className="flex overflow-x-auto scrollbar-hide pb-3"
              style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
            >
              <div className="flex gap-6 px-12">
                {[...bottomRowCards, ...bottomRowCards, ...bottomRowCards].map((card, index) => (
                  <motion.div 
                    key={`bottom-card-${index}`}
                    className="min-w-72 max-w-72 bg-white border border-gray-100 rounded-lg p-6 shadow-sm hover:shadow-md flex flex-col"
                    onMouseEnter={() => setHoveredCardId(`bottom-card-${index}`)}
                    onMouseLeave={() => setHoveredCardId(null)}
                    initial={{ opacity: 0 }}
                    animate={{ 
                      opacity: 1, 
                      scale: hoveredCardId === `bottom-card-${index}` ? 1.02 : 1,
                      transition: { duration: 0.3 }
                    }}
                    whileHover={{ boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)" }}
                    style={{ zIndex: hoveredCardId === `bottom-card-${index}` ? 10 : 1 }}
                  >
                    <div className="flex items-center mb-3">
                      <div className="mr-3">{card.icon}</div>
                      <h3 className="text-base font-medium text-blue-800">{card.title}</h3>
                    </div>
                    <p className="text-blue-800/80 text-sm">{card.description}</p>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </motion.section>
  );
};

export default ScrollingCards;