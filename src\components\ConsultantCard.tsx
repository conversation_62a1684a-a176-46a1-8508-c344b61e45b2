import React from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { Star, GraduationCap, Briefcase, MapPin, Link, Award, School, University } from 'lucide-react';

// Helper function to format college name into a domain
const formatCollegeToDomain = (collegeName: string) => {
  const normalizedCollege = collegeName.trim().toLowerCase().replace(/\s+/g, '');
  return `${normalizedCollege}.edu`;
};

// Helper function to split attended colleges and return logos with hover tooltip
const renderCollegeLogos = (attendedSchool: string) => {
  if (!attendedSchool) return null;

  // Split the attendedSchool string by commas (or any other delimiter you use)
  const colleges = attendedSchool.split(',');

  // Generate image tags for each college with a hover tooltip
  return (
    <div className="flex flex-wrap items-center ml-6">
      {colleges.map((college, index) => {
        const domain = formatCollegeToDomain(college);
        return (
          <div key={index} className="relative group">
            <img
              src={`https://img.logo.dev/${domain}?token=pk_Hu5PPvRXQ8ysemxzEKvY2A`}
              alt={`${college} logo`}
              className="w-12 h-12 sm:w-10 sm:h-10 mr-2 mb-2 cursor-pointer"
            />
            {/* Tooltip */}
            <div className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 bg-black text-white text-xs rounded-lg py-1 px-2 whitespace-nowrap z-10">
              {college.trim()}
            </div>
          </div>
        );
      })}
    </div>
  );
};

interface ConsultantData {
  firstName: string;
  lastName: string;
  profilePic: string;
  bio: string;
  college: string;
  acceptedColleges: string;
  attendedSchool: string;
  testScores: string;
  intendedMajor: string;
  location: string;
  education: string;
  website: string;
  email: string;
}

interface ConsultantCardProps {
  consultant: ConsultantData;
  onClick?: () => void; // Add onClick prop
}

const ConsultantCard: React.FC<ConsultantCardProps> = ({ consultant, onClick }) => {
  return (
    <motion.div
      onClick={onClick} // Apply onClick here
      className="bg-white shadow-xl rounded-lg overflow-hidden w-full lg:w-[300%] h-[100%] max-w-none mx-auto transition-transform duration-150 transform hover:scale-105 lg:-ml-96"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      style={{ cursor: onClick ? 'pointer' : 'default' }} // Change cursor to pointer when clickable
    >
      {/* Gradient Header */}
      <div className="relative h-28 bg-gradient-to-r from-blue-500 to-purple-600">
        {/* Consultant Badge */}
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: 'spring', stiffness: 260, damping: 20 }}
          className="absolute top-4 right-4 bg-yellow-400 text-blue-900 px-2 py-1 rounded-full flex items-center shadow-lg"
        >
          <Star className="w-4 h-4 mr-1" />
          <span className="font-semibold text-sm">Consultant</span>
        </motion.div>

        {/* Profile Image */}
        <div className="absolute -bottom-10 left-6">
          <div className="w-24 h-24 rounded-full overflow-hidden border-4 border-white">
            <Image
              src={consultant.profilePic}
              alt={`${consultant.firstName} ${consultant.lastName}'s profile picture`}
              width={96}
              height={96}
              className="object-cover"
              style={{ objectFit: 'cover', aspectRatio: '1 / 1' }}
            />
          </div>
        </div>
      </div>

      {/* Consultant Information */}
      <div className="pt-14 px-8 pb-8 flex flex-col lg:flex-row items-start space-y-8 lg:space-y-0 lg:space-x-8">
        {/* Name and Major */}
        <div className="flex flex-col lg:w-1/3 w-full">
          <h2 className="text-2xl font-bold text-gray-900">
            {consultant.firstName} {consultant.lastName}
          </h2>
          <p className="text-lg text-blue-600 mb-4">{consultant.intendedMajor || 'Intended Major'}</p>
          <p className="text-gray-700 mb-6 text-sm">{consultant.bio || 'Bio not provided'}</p>
        </div>

        {/* Contact and Education Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full lg:w-2/3 text-sm">
          <ProfileItem icon={GraduationCap} label="Education" value={consultant.education} />
          <ProfileItem icon={Briefcase} label="School" value={consultant.college} />
          <ProfileItem icon={MapPin} label="Location" value={consultant.location} />
          <ProfileItem icon={Link} label="Website" value={consultant.website || 'No website available'} />
          <ProfileItem icon={School} label="Test Scores" value={consultant.testScores || 'Not specified'} />
          <ProfileItem icon={Award} label="Accepted Colleges" value={consultant.acceptedColleges || 'Not specified'} />

          <div className="flex flex-col space-y-2">
            {/* Label with the icon */}
            <div className="flex items-center space-x-2">
              <University className="text-blue-500 h-5 w-5 flex-shrink-0" />
              <span className="font-semibold">Schools Attended:</span>
            </div>

            {/* Render College Logos below */}
            <div className="flex flex-wrap items-center mt-2">
              {renderCollegeLogos(consultant.attendedSchool)}
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

// Updated Profile Item for display
interface ProfileItemProps {
  icon: React.ElementType;
  label: string;
  value: string;
}

const ProfileItem: React.FC<ProfileItemProps> = ({ icon: Icon, label, value }) => {
  return (
    <div className="flex flex-col space-y-1 text-gray-700">
      <div className="flex items-center space-x-2">
        <Icon className="text-blue-500 h-5 w-5 flex-shrink-0" />
        <span className="font-semibold">{label}:</span>
      </div>
      <p className="ml-7 break-words">{value}</p>
    </div>
  );
};

export default ConsultantCard;
