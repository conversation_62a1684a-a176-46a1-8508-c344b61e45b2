'use client';

import React, { useEffect } from 'react';
import LegalLayout from '@/components/LegalLayout';

const TermsOfService = () => {
  useEffect(() => {
    document.title = 'Klinn | Terms of Service';
  }, []);

  return (
    <LegalLayout title="Terms of Service" lastUpdated="3/10/2025">
      <div className="prose prose-blue max-w-none">
        <p>Welcome to Klinn LLC (&quot;Klinn,&quot; &quot;we,&quot; &quot;our,&quot; or &quot;us&quot;). By accessing or using our website, services, or tools (collectively, the &quot;Services&quot;), you agree to these Terms of Service (&quot;Terms&quot;). Please read them carefully. If you do not agree to these Terms, you must not use our Services.</p>
        
        <p className="text-sm text-gray-600">Website: https://klinn.works</p>

        <h2>1. Acceptance of Terms</h2>
        <p>By using our Services, you affirm that you are at least 13 years of age or have the legal capacity to enter into these Terms. If you are under 18, you may use our Services only with the involvement of a parent or legal guardian.</p>

        <h2>2. Services Overview</h2>
        <p>Klinn LLC is a platform designed to help users explore opportunities in healthcare education and clinical experiences. We do not guarantee the accuracy, completeness, or availability of any content, services, or third-party links provided on our platform. Additionally, we are not responsible for the accuracy of content displayed on our platform, including but not limited to user-generated content and third-party contributions.</p>

        <h2>3. User Responsibilities</h2>
        <p>By using our Services, you agree to:</p>
        <ul>
          <li>Comply with all applicable laws and regulations.</li>
          <li>Refrain from using the Services to distribute harmful, illegal, or objectionable content.</li>
          <li>Avoid attempting to interfere with or compromise the integrity of our systems or Services.</li>
          <li>Accurately represent your identity and affiliation.</li>
        </ul>

        <h2>4. No Guarantees or Warranties</h2>
        <p>All information and services provided on Klinn LLC are offered on an &quot;as is&quot; and &quot;as available&quot; basis without any express or implied warranties. We do not guarantee:</p>
        <ul>
          <li>That the Services will meet your needs or expectations.</li>
          <li>That the Services will be uninterrupted, error-free, or secure.</li>
          <li>The accuracy or reliability of information provided by other users or third-party partners.</li>
          <li>The accuracy of any content displayed on our platform.</li>
        </ul>

        <h2>5. Limitation of Liability</h2>
        <p>To the maximum extent permitted by law, Klinn LLC, its officers, employees, and affiliates will not be liable for any direct, indirect, incidental, special, or consequential damages arising from your use of or inability to use the Services.</p>
        <p>This includes, but is not limited to, damages for loss of profits, data, goodwill, or other intangible losses, even if Klinn LLC has been advised of the possibility of such damages.</p>

        <h2>6. Third-Party Content</h2>
        <p>Our Services may include links to third-party websites or content. Klinn LLC is not responsible for the accuracy, legality, or content of external websites or services.</p>

        <h2>7. Indemnification</h2>
        <p>You agree to indemnify and hold Klinn LLC harmless from any claims, damages, or liabilities arising out of your use of the Services, your violation of these Terms, or your infringement of any third-party rights.</p>

        <h2>8. Changes to the Terms</h2>
        <p>We may update these Terms at any time. Changes will be effective upon posting. Continued use of the Services after changes constitutes acceptance of the new Terms.</p>

        <h2>9. Contact Us</h2>
        <p>If you have any questions or concerns about these Terms, please contact <NAME_EMAIL>.</p>

        <p className="mt-8 font-semibold">By using Klinn LLC, you agree to this Terms of Service and our Privacy Policy.</p>
      </div>
    </LegalLayout>
  );
};

export default TermsOfService;